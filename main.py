from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pathlib import Path
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
import json
import fitz  # PyMuPDF
from datetime import datetime
import os
import tempfile
import time
import random
from openai import OpenAI
import asyncio
import uuid

# Configuration Constants
BASE_DIR = Path("storyteller_cards")
PDF_DIR = BASE_DIR / "pdfs"
CARD_SETS_DIR = BASE_DIR / "card_sets"
STORY_PATTERNS_DIR = BASE_DIR / "story_patterns"
PROGRESS_DIR = BASE_DIR / "progress"
STATIC_DIR = Path("static")

# Rate limiting constants
MAX_RETRIES = 5
INITIAL_RETRY_DELAY = 2
MAX_RETRY_DELAY = 60

# Story Categories and Frameworks (same as original)
STORY_CATEGORIES = {
    "Stories that Sell": "Build trust and convince people of your ability to deliver. Trust is essential for selling, more than just price or quality.",
    "Stories that Motivate": "Inspire people to support your ideas and take action. Show your plan and your underlying motivations.",
    "Stories that Convince": "Explain complex information to a non-expert audience and build trust in your judgment. Avoid overwhelming with data; focus on understanding and trust.",
    "Stories that Connect": "Foster empathy and understanding by showing different perspectives. Stories help people see things from another point of view.",
    "Stories that Explain": "Make abstract strategies understandable and relevant. Bring your strategic direction to life.",
    "Stories that Lead": "Build a stronger and more cohesive team. Share stories of struggles, triumphs, and learnings within the team.",
    "Stories that Impress": "Present ideas confidently and clearly. Avoid jargon and use storytelling techniques to engage your audience."
}

STORY_FRAMEWORKS = {
    # Stories that Sell
    "Simple Sales Stories": "Share relatable success stories of helping others. Use stories about existing customers to convince new ones.",
    "Social Proof": "Use trends, prototypes, and testimonials to strengthen your case.",
    "Rags to Riches": "Tell optimistic stories with the customer as the central figure. A story of rising to success from humble beginnings.",
    "Pitch Perfect": "Condense your message into a concise elevator pitch. Craft compelling pitches using Problem, Solution, Trust formula.",
    "Audience Profile": "Understand your audience and their problems to create targeted sales stories.",
    
    # Stories that Motivate
    "Dragon & the City": "Explain your overarching goals and vision. Frame projects as overcoming a threat to improve the status quo.",
    "Drive Stories": "Articulate your motivations and connect them with your audience's potential motivations.",
    "Three Great Conflicts": "Identify and address the obstacles you need to overcome through fundamental human struggles.",
    "Innovation Curve": "Reassure your audience about the risks involved in your idea based on their risk tolerance.",
    "No Easy Way": "Provide a realistic outlook on the challenges ahead. Acknowledges setbacks on the path to success.",
    
    # Stories that Convince
    "Three is the Magic Number": "Prioritize key facts your audience can remember using patterns of three.",
    "That's Funny": "Share the excitement and insights behind your discoveries. Find unexpected moments that reveal insights.",
    "Data Detectives": "Present data in a narrative format to improve understanding and recall.",
    "Trust Me, I'm an Expert": "Demonstrate your credibility and expertise by showing your values through stories of your actions.",
    "Hero & Guide": "Position yourself as the expert guide assisting your audience. Your user is the hero; you are the guide.",
    
    # Stories that Connect
    "Story Listening": "Understand others by listening to their experiences and stories.",
    "Abstractions": "Observe behavior in addition to asking questions to understand deeper knowledge.",
    "Universal Stories": "Find common ground and shared experiences that resonate across cultures.",
    "Story-ish Conversations": "Look for stories in everyday interactions to uncover insights about people and change.",
    "Circle of Life": "Develop relatable stories based on universal characters and life stages.",
    
    # Stories that Explain
    "Order & Chaos": "Show where your strategy fits in a changing world. Balance between the known and unknown.",
    "Good & Evil": "Define the important battles your strategy addresses through moral conflicts and choices.",
    "What's it About?": "Explain the relevance of the strategy to your colleagues by focusing on change and benefit.",
    "Rolls Royce Moment": "Illustrate your strategy in action with a vivid, exemplary detail that represents the whole story.",
    "Story Hooks": "Make your strategy sound engaging with compelling openings that capture attention.",
    
    # Stories that Lead
    "Curious Tales": "Discover what motivates your team members by exploring what grabs attention.",
    "Man in a Hole": "Frame teamwork as an epic journey with challenges, crisis, and recovery with newfound wisdom.",
    "Emotional Dashboard": "Find stories in the highs and lows of projects by identifying strong emotions.",
    "Thoughtful Failures": "Extract lessons from mistakes by analyzing goals, assumptions, insights, skills, and communication.",
    "Story Bank": "Collect and share valuable team stories for future reference and learning.",
    
    # Stories that Impress
    "Movie Time": "Tell a story, not just present facts. Use vivid descriptions to create a mental movie for your audience.",
    "Five Ts": "Structure your story effectively using Timeline, Turning Points, Tensions, Temptations, Teachable Moments.",
    "Show and Tell": "Make visuals and narration work together effectively to maintain audience attention.",
    "Cut to the Chase": "Have a backup plan if your presentation falters. Regain audience attention by focusing on Action, Emotion, or Meaning.",
    "Secrets & Puzzles": "Engage the audience by hinting at undiscovered information or anomalies to create intrigue."
}

FRAMEWORK_CATEGORY_MAP = {
    "Simple Sales Stories": "Stories that Sell",
    "Social Proof": "Stories that Sell",
    "Rags to Riches": "Stories that Sell",
    "Pitch Perfect": "Stories that Sell",
    "Audience Profile": "Stories that Sell",
    
    "Dragon & the City": "Stories that Motivate",
    "Drive Stories": "Stories that Motivate",
    "Three Great Conflicts": "Stories that Motivate",
    "Innovation Curve": "Stories that Motivate",
    "No Easy Way": "Stories that Motivate",
    
    "Three is the Magic Number": "Stories that Convince",
    "That's Funny": "Stories that Convince",
    "Data Detectives": "Stories that Convince",
    "Trust Me, I'm an Expert": "Stories that Convince",
    "Hero & Guide": "Stories that Convince",
    
    "Story Listening": "Stories that Connect",
    "Abstractions": "Stories that Connect",
    "Universal Stories": "Stories that Connect",
    "Story-ish Conversations": "Stories that Connect",
    "Circle of Life": "Stories that Connect",
    
    "Order & Chaos": "Stories that Explain",
    "Good & Evil": "Stories that Explain",
    "What's it About?": "Stories that Explain",
    "Rolls Royce Moment": "Stories that Explain",
    "Story Hooks": "Stories that Explain",
    
    "Curious Tales": "Stories that Lead",
    "Man in a Hole": "Stories that Lead",
    "Emotional Dashboard": "Stories that Lead",
    "Thoughtful Failures": "Stories that Lead",
    "Story Bank": "Stories that Lead",
    
    "Movie Time": "Stories that Impress",
    "Five Ts": "Stories that Impress",
    "Show and Tell": "Stories that Impress",
    "Cut to the Chase": "Stories that Impress",
    "Secrets & Puzzles": "Stories that Impress"
}

STORY_ELEMENTS = {
    "Characters": "People or entities that drive the narrative or are affected by events",
    "Setting": "The context, environment, or backdrop where events take place",
    "Conflict": "The central problem, challenge, or tension that drives the story",
    "Resolution": "How problems are solved or situations are concluded",
    "Emotion": "The feelings evoked or expressed in the narrative",
    "Surprise": "Unexpected turns, revelations, or insights that captivate attention",
    "Metaphor": "Symbolic comparisons that illustrate complex ideas through familiar concepts",
    "Data Point": "Compelling statistics, numbers, or facts that strengthen the narrative",
    "Quotable Moment": "Memorable phrases, sayings, or statements worth repeating",
    "Transformation": "Changes in perspective, situation, or understanding over time"
}

# Pydantic Models
class StoryCard(BaseModel):
    title: str
    framework: str
    category: str
    emotional_core: str
    content: str
    key_insight: str
    contextual_relevance: str
    storyteller_script: str
    script_hook: str = ""
    script_narrative: str = ""
    script_conclusion: str = ""
    script_delivery: str = ""
    key_phrases: List[str]
    audience_impact: str
    interest_score: int
    audience: List[str]
    source: str
    page_reference: List[int]
    entities: List[str] = []
    sentiment: str = ""
    keywords: List[str] = []

class StoryChunk(BaseModel):
    pages: List[int]
    text: str
    has_story_potential: bool = False
    story_frameworks: List[str] = []
    entities: List[str] = []
    sentiment: str = ""
    keywords: List[str] = []

class ProcessingRequest(BaseModel):
    chunk_size: int = 5
    start_page: int = 1
    end_page: Optional[int] = None
    story_elements: List[str] = []

class ProcessingStatus(BaseModel):
    status: str
    progress: float
    message: str
    current_chunk: int = 0
    total_chunks: int = 0
    cards_extracted: int = 0

# Global storage for processing status
processing_status: Dict[str, ProcessingStatus] = {}

app = FastAPI(title="Storyteller Tactics Card Generator")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

def setup_directories():
    """Create all necessary directories"""
    for directory in [PDF_DIR, CARD_SETS_DIR, STORY_PATTERNS_DIR, PROGRESS_DIR, STATIC_DIR]:
        directory.mkdir(parents=True, exist_ok=True)

def save_card_set(story_cards: List[dict], pdf_name: str):
    """Save story card set to JSON file"""
    output_path = CARD_SETS_DIR / f"{pdf_name.replace('.pdf', '')}_cards.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump({"story_cards": story_cards}, f, indent=2)
    return output_path

def load_existing_card_set(pdf_name: str) -> List[dict]:
    """Load existing story card set"""
    cards_file = CARD_SETS_DIR / f"{pdf_name.replace('.pdf', '')}_cards.json"
    if cards_file.exists():
        with open(cards_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data['story_cards']
    return []

def process_with_retry(client, messages, max_retries=MAX_RETRIES):
    """Process API request with exponential backoff retry logic"""
    for attempt in range(max_retries):
        try:
            response = client.chat.completions.create(
                model="o4-mini",
                messages=messages,
                response_format={"type": "text"},
                reasoning_effort="medium"
            )
            return response
        except Exception as e:
            error_str = str(e)
            
            if "429" in error_str or "rate_limit" in error_str.lower():
                if attempt < max_retries - 1:
                    delay = min(INITIAL_RETRY_DELAY * (2 ** attempt) + random.uniform(0, 1), MAX_RETRY_DELAY)
                    time.sleep(delay)
                else:
                    raise Exception(f"Maximum retries reached. Error: {e}")
            else:
                raise Exception(f"API Error: {e}")
    
    raise Exception("Maximum retries exceeded")

def prepare_text_chunks(pdf_document, chunk_size: int, start_page: int = 1, end_page: Optional[int] = None) -> List[StoryChunk]:
    """Divide the PDF into multi-page chunks for story extraction"""
    chunks = []
    total_pages = pdf_document.page_count
    
    if end_page is None or end_page > total_pages:
        end_page = total_pages
    
    actual_start_page = max(0, start_page - 1)
    actual_end_page = min(end_page, total_pages)
    
    for start in range(actual_start_page, actual_end_page, chunk_size):
        end = min(start + chunk_size, actual_end_page)
        
        chunk_text = ""
        page_numbers = []
        for page_num in range(start, end):
            page = pdf_document[page_num]
            page_text = page.get_text()
            chunk_text += f"\n\n--- PAGE {page_num + 1} ---\n\n{page_text}"
            page_numbers.append(page_num + 1)
        
        chunk = StoryChunk(
            pages=page_numbers,
            text=chunk_text
        )
        chunks.append(chunk)
    
    return chunks

def analyze_story_potential(client, chunk: StoryChunk) -> StoryChunk:
    """Enhanced analysis of text chunk to determine story potential"""
    
    framework_descriptions = []
    for fw in list(STORY_FRAMEWORKS.keys())[:10]:  # Limit for token efficiency
        framework_descriptions.append(f"{fw}: {STORY_FRAMEWORKS[fw]}")
    
    element_descriptions = []
    for element in STORY_ELEMENTS:
        element_descriptions.append(f"{element}: {STORY_ELEMENTS[element]}")

    system_prompt = f"""Analyze this chunk of text from a book to determine if it contains potential material for storytelling.

Perform an in-depth analysis including:
1. Sentiment analysis - identify the overall emotional tone (positive, negative, neutral, mixed)
2. Named entity recognition - identify key people, organizations, locations, events
3. Keyword extraction - identify the most important themes and topics
4. Story potential assessment - evaluate if the text contains compelling narrative elements

Focus on identifying if this text contains one or more of these story elements:
{'; '.join(element_descriptions)}

Specifically, evaluate if this text could be used to create story cards fitting any of these storytelling frameworks:
{'; '.join(framework_descriptions)}

Your response should be in JSON format with the following structure:
{{
  "has_story_potential": true/false,
  "story_frameworks": ["Framework 1", "Framework 2", ...],
  "sentiment": "Overall emotional tone (positive, negative, neutral, mixed)",
  "entities": ["Entity 1", "Entity 2", ...],  
  "keywords": ["Theme 1", "Theme 2", ...],
  "reasoning": "Detailed explanation of why this chunk has story potential and which frameworks it might fit"
}}"""
    
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"Text chunk to analyze: {chunk.text[:8000]}"}
    ]
    
    try:
        response = process_with_retry(client, messages)
        
        # Try to parse JSON from response
        response_text = response.choices[0].message.content
        
        # Extract JSON if it's wrapped in markdown
        if "```json" in response_text:
            json_start = response_text.find("```json") + 7
            json_end = response_text.find("```", json_start)
            response_text = response_text[json_start:json_end]
        elif "```" in response_text:
            json_start = response_text.find("```") + 3
            json_end = response_text.rfind("```")
            response_text = response_text[json_start:json_end]
        
        response_json = json.loads(response_text)
        
        # Update the chunk with analysis results
        chunk.has_story_potential = response_json.get("has_story_potential", False)
        chunk.story_frameworks = response_json.get("story_frameworks", [])
        chunk.sentiment = response_json.get("sentiment", "")
        chunk.entities = response_json.get("entities", [])
        chunk.keywords = response_json.get("keywords", [])
        
        return chunk
        
    except Exception as e:
        print(f"Error analyzing story potential: {e}")
        return chunk

def extract_story_cards(client, chunk: StoryChunk) -> List[dict]:
    """Enhanced extraction of story cards with improved script structure"""
    
    if not chunk.has_story_potential or not chunk.story_frameworks:
        return []
    
    framework_descriptions = []
    for fw in chunk.story_frameworks:
        if fw in STORY_FRAMEWORKS:
            framework_descriptions.append(f"{fw}: {STORY_FRAMEWORKS[fw]}")
    
    system_prompt = f"""Create compelling story cards based on the text chunk provided.

For each potential story in this text, create a card that follows the Storyteller Tactics framework. Focus on these frameworks that were identified in this text:
{'; '.join(framework_descriptions)}

For each story card, provide:
1. A compelling title that captures the essence of the story
2. The storytelling framework it fits (from the list above)
3. The category it belongs to (based on the framework)
4. The emotional core - identify the primary emotion this story evokes
5. Detailed content - provide a rich and comprehensive summary of the story
6. Key insight - extract the central lesson or revelation from this story
7. Contextual relevance - explain when and where this story is most effectively used
8. A storyteller script with structured sections
9. Key phrases - list 3-5 memorable phrases or powerful quotes from the story
10. Audience impact - describe how this story will affect listeners
11. Appropriate audiences
12. An interest score (1-10) indicating how engaging this would be
13. Source - identify the original author and title if available
14. Entities - important people, organizations, or places in the story
15. Keywords - 3-7 key themes or topics in the story
16. Sentiment - the overall emotional tone of the story

Your response should be in JSON format with the following structure:
{{
  "story_cards": [
    {{
      "title": "Compelling title for the story",
      "framework": "One of the frameworks from the list",
      "category": "The corresponding category for this framework",
      "emotional_core": "The primary emotion this story evokes",
      "content": "Detailed and rich description of the story",
      "key_insight": "The central lesson or revelation from this story",
      "contextual_relevance": "When and where this story is most effectively used",
      "storyteller_script": "Complete script with Hook, Narrative, Conclusion, and Delivery sections",
      "script_hook": "Attention-grabbing opening",
      "script_narrative": "Main body of the story",
      "script_conclusion": "Impactful closing",
      "script_delivery": "Delivery guidance",
      "key_phrases": ["phrase1", "phrase2", "phrase3"],
      "audience_impact": "How this story will affect the audience",
      "audience": ["audience1", "audience2"],
      "interest_score": 8,
      "source": "Author_Title",
      "page_reference": {chunk.pages},
      "entities": ["Entity1", "Entity2"],
      "keywords": ["Theme1", "Theme2"],
      "sentiment": "positive"
    }}
  ]
}}"""
    
    additional_context = f"""
Based on preliminary analysis, this text:
- Has these named entities: {', '.join(chunk.entities)}
- Contains these themes: {', '.join(chunk.keywords)}
- Has an overall {chunk.sentiment} sentiment tone

Please use this information to create richer, more targeted story cards.
"""
    
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"{additional_context}\n\nText chunk (from pages {chunk.pages[0]}-{chunk.pages[-1]}):\n{chunk.text[:10000]}"}
    ]
    
    try:
        response = process_with_retry(client, messages)
        response_text = response.choices[0].message.content
        
        # Extract JSON if it's wrapped in markdown
        if "```json" in response_text:
            json_start = response_text.find("```json") + 7
            json_end = response_text.find("```", json_start)
            response_text = response_text[json_start:json_end]
        elif "```" in response_text:
            json_start = response_text.find("```") + 3
            json_end = response_text.rfind("```")
            response_text = response_text[json_start:json_end]
        
        response_json = json.loads(response_text)
        story_cards = response_json.get("story_cards", [])
        
        # Ensure all required fields and correct category mapping
        for card in story_cards:
            framework = card.get("framework", "")
            if framework in FRAMEWORK_CATEGORY_MAP:
                card["category"] = FRAMEWORK_CATEGORY_MAP[framework]
            
            # Ensure all required fields are present
            for field, default_value in [
                ("emotional_core", "Inspiration"),
                ("key_insight", "Key lesson from the story"),
                ("contextual_relevance", "Best used in professional settings"),
                ("key_phrases", ["Key phrase 1", "Key phrase 2"]),
                ("audience_impact", "Helps listeners understand the importance"),
                ("source", "Unknown"),
                ("entities", []),
                ("keywords", []),
                ("sentiment", "mixed"),
                ("script_hook", "Attention-grabbing opening"),
                ("script_narrative", "Main story content"),
                ("script_conclusion", "Impactful closing"),
                ("script_delivery", "Delivery guidance"),
            ]:
                if field not in card:
                    card[field] = default_value
        
        return story_cards
        
    except Exception as e:
        print(f"Error extracting story cards: {e}")
        return []

async def process_pdf_background(session_id: str, pdf_path: str, pdf_name: str, request: ProcessingRequest):
    """Background task to process PDF and extract story cards"""
    
    try:
        # Initialize OpenAI client
        client = OpenAI(api_key=os.environ.get("GEMINI_API_KEY"))
        
        # Update status
        processing_status[session_id] = ProcessingStatus(
            status="starting",
            progress=0.0,
            message="Loading PDF and preparing chunks..."
        )
        
        # Load existing cards if available
        story_cards = load_existing_card_set(pdf_name)
        
        # Create PDF document object
        pdf_document = fitz.open(pdf_path)
        
        # Prepare chunks
        chunks = prepare_text_chunks(
            pdf_document, 
            request.chunk_size, 
            request.start_page, 
            request.end_page
        )
        
        total_chunks = len(chunks)
        processing_status[session_id].total_chunks = total_chunks
        processing_status[session_id].message = f"Processing {total_chunks} chunks..."
        
        # Process each chunk
        for chunk_idx, chunk in enumerate(chunks):
            processing_status[session_id].current_chunk = chunk_idx + 1
            processing_status[session_id].progress = (chunk_idx + 1) / total_chunks
            processing_status[session_id].message = f"Processing chunk {chunk_idx + 1}/{total_chunks} (pages {chunk.pages[0]}-{chunk.pages[-1]})"
            
            # Analyze story potential
            analyzed_chunk = analyze_story_potential(client, chunk)
            
            # Extract story cards if potential found
            if analyzed_chunk.has_story_potential:
                new_cards = extract_story_cards(client, analyzed_chunk)
                story_cards.extend(new_cards)
                processing_status[session_id].cards_extracted = len(story_cards)
                processing_status[session_id].message = f"Extracted {len(new_cards)} cards from chunk {chunk_idx + 1}"
            
            # Small delay to prevent overwhelming the API
            await asyncio.sleep(0.1)
        
        # Save final results
        save_card_set(story_cards, pdf_name)
        
        processing_status[session_id].status = "completed"
        processing_status[session_id].progress = 1.0
        processing_status[session_id].message = f"Processing complete! Extracted {len(story_cards)} story cards total."
        
        pdf_document.close()
        
    except Exception as e:
        processing_status[session_id].status = "error"
        processing_status[session_id].message = f"Error processing PDF: {str(e)}"

# Initialize directories
setup_directories()

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """Serve the main HTML page"""
    html_path = STATIC_DIR / "index.html"
    if html_path.exists():
        return FileResponse(html_path)
    else:
        return HTMLResponse("<h1>Storyteller Tactics Card Generator</h1><p>Frontend not found. Please add the HTML file to the static directory.</p>")

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload PDF file"""
    if not file.filename.endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are allowed")
    
    # Save uploaded file
    file_path = PDF_DIR / file.filename
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    # Get PDF info
    pdf_document = fitz.open(file_path)
    total_pages = pdf_document.page_count
    pdf_document.close()
    
    # Check for existing cards
    existing_cards = load_existing_card_set(file.filename)
    
    return {
        "filename": file.filename,
        "size": len(content),
        "pages": total_pages,
        "existing_cards": len(existing_cards)
    }

@app.post("/process/{filename}")
async def process_pdf(filename: str, request: ProcessingRequest, background_tasks: BackgroundTasks):
    """Start processing PDF to extract story cards"""
    
    # Check if API key is set
    if not os.environ.get("GEMINI_API_KEY"):
        raise HTTPException(status_code=500, detail="GEMINI_API_KEY environment variable not set")
    
    pdf_path = PDF_DIR / filename
    if not pdf_path.exists():
        raise HTTPException(status_code=404, detail="PDF file not found")
    
    # Generate session ID
    session_id = str(uuid.uuid4())
    
    # Start background processing
    background_tasks.add_task(process_pdf_background, session_id, str(pdf_path), filename, request)
    
    return {"session_id": session_id}

@app.get("/status/{session_id}")
async def get_processing_status(session_id: str):
    """Get processing status"""
    if session_id not in processing_status:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return processing_status[session_id]

@app.get("/cards/{filename}")
async def get_story_cards(filename: str):
    """Get extracted story cards"""
    cards = load_existing_card_set(filename)
    if not cards:
        raise HTTPException(status_code=404, detail="No story cards found")
    
    return {"story_cards": cards}

@app.get("/export/{filename}/markdown")
async def export_markdown(filename: str):
    """Export story cards as markdown"""
    cards = load_existing_card_set(filename)
    if not cards:
        raise HTTPException(status_code=404, detail="No story cards found")
    
    # Generate markdown content
    markdown_content = f"# Storyteller Tactics Cards: {filename}\n"
    markdown_content += f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
    
    # Group by category
    categories = {}
    for card in cards:
        category = card.get("category", "Uncategorized")
        if category not in categories:
            categories[category] = []
        categories[category].append(card)
    
    for category, category_cards in categories.items():
        markdown_content += f"## {category}\n\n"
        if category in STORY_CATEGORIES:
            markdown_content += f"_{STORY_CATEGORIES[category]}_\n\n"
        
        for card in category_cards:
            markdown_content += f"### {card.get('title', 'Story Card')}\n\n"
            markdown_content += f"**Framework:** {card.get('framework', 'N/A')}  \n"
            markdown_content += f"**Emotional Core:** {card.get('emotional_core', 'N/A')}  \n"
            markdown_content += f"**Content:** {card.get('content', '')}  \n\n"
            markdown_content += f"**Key Insight:** {card.get('key_insight', '')}  \n\n"
            markdown_content += f"**Interest Score:** {card.get('interest_score', 'N/A')}/10  \n\n"
            markdown_content += "---\n\n"
    
    # Save markdown file
    markdown_path = CARD_SETS_DIR / f"{filename.replace('.pdf', '')}_cards.md"
    with open(markdown_path, 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    
    return FileResponse(
        markdown_path,
        media_type='text/markdown',
        filename=f"{filename.replace('.pdf', '')}_storyteller_cards.md"
    )

@app.get("/categories")
async def get_categories():
    """Get available story categories and frameworks"""
    return {
        "categories": STORY_CATEGORIES,
        "frameworks": STORY_FRAMEWORKS,
        "elements": STORY_ELEMENTS
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
@echo off
echo 🃏 Storyteller Tactics Card Generator
echo ==================================================

REM Check if API key is set
if "%GEMINI_API_KEY%"=="" (
    echo ❌ GEMINI_API_KEY environment variable not set
    echo Please set your OpenAI API key first:
    echo set GEMINI_API_KEY=your_api_key_here
    echo.
    echo Then run this script again.
    pause
    exit /b 1
)

echo ✅ API key found

REM Install dependencies
echo 📦 Installing dependencies...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed

REM Start server
echo 🚀 Starting server...
echo Server will be available at: http://localhost:8000
echo Press Ctrl+C to stop the server
echo.

REM Start server in background and open browser
start "" http://localhost:8000
uvicorn main:app --reload --host 0.0.0.0 --port 8000

pause
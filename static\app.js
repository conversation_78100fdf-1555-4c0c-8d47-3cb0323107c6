// Global variables
let currentFile = null;
let currentSessionId = null;
let processingInterval = null;
let allCards = [];

// Initialize animations
document.addEventListener('DOMContentLoaded', function() {
    anime({
        targets: '.fade-in',
        opacity: [0, 1],
        translateY: [20, 0],
        duration: 800,
        delay: anime.stagger(100),
        easing: 'easeOutCubic'
    });
});

// Tab Management
function showTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('tab-active');
    });
    event.target.classList.add('tab-active');

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    document.getElementById(tabName + '-tab').classList.remove('hidden');

    // Load cards if switching to cards tab
    if (tabName === 'cards' && currentFile) {
        loadStoryCards();
    } else if (tabName === 'analytics' && currentFile) {
        loadAnalytics();
    }
}

// File handling
async function handleFileSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('file', file);

    try {
        const response = await fetch('/upload', {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            const fileInfo = await response.json();
            currentFile = fileInfo;
            displayFileInfo(fileInfo);
            
            // Animate file info appearance
            anime({
                targets: '#fileInfo',
                opacity: [0, 1],
                translateY: [20, 0],
                duration: 500,
                easing: 'easeOutCubic'
            });
        } else {
            showAlert('Error uploading file', 'error');
        }
    } catch (error) {
        showAlert('Error uploading file: ' + error.message, 'error');
    }
}

function displayFileInfo(fileInfo) {
    document.getElementById('fileName').textContent = fileInfo.filename;
    document.getElementById('fileSize').textContent = (fileInfo.size / 1024).toFixed(2) + ' KB';
    document.getElementById('filePages').textContent = fileInfo.pages;
    
    document.getElementById('fileInfo').classList.remove('hidden');
    document.getElementById('configSection').classList.remove('hidden');
    document.getElementById('processBtn').disabled = false;

    // Update end page max value
    document.getElementById('endPage').max = fileInfo.pages;
    document.getElementById('endPage').value = fileInfo.pages;

    if (fileInfo.existing_cards > 0) {
        showAlert(`Found ${fileInfo.existing_cards} existing story cards`, 'success');
    }
}

// Processing
async function startProcessing() {
    if (!currentFile) return;

    const config = {
        chunk_size: parseInt(document.getElementById('chunkSize').value),
        start_page: parseInt(document.getElementById('startPage').value),
        end_page: parseInt(document.getElementById('endPage').value),
        story_elements: getSelectedElements()
    };

    try {
        const response = await fetch(`/process/${currentFile.filename}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        });

        if (response.ok) {
            const result = await response.json();
            currentSessionId = result.session_id;
            
            document.getElementById('processingStatus').classList.remove('hidden');
            document.getElementById('processBtn').disabled = true;
            
            // Start polling for status
            processingInterval = setInterval(checkProcessingStatus, 2000);
            
            // Animate processing card
            anime({
                targets: '#processingStatus',
                opacity: [0, 1],
                translateY: [20, 0],
                duration: 500,
                easing: 'easeOutCubic'
            });
        } else {
            showAlert('Error starting processing', 'error');
        }
    } catch (error) {
        showAlert('Error starting processing: ' + error.message, 'error');
    }
}

async function checkProcessingStatus() {
    if (!currentSessionId) return;

    try {
        const response = await fetch(`/status/${currentSessionId}`);
        if (response.ok) {
            const status = await response.json();
            updateProcessingUI(status);

            if (status.status === 'completed' || status.status === 'error') {
                clearInterval(processingInterval);
                processingInterval = null;

                if (status.status === 'completed') {
                    showAlert('Processing completed successfully!', 'success');
                    loadStoryCards();
                } else {
                    showAlert('Processing failed: ' + status.message, 'error');
                }

                document.getElementById('processBtn').disabled = false;
            }
        }
    } catch (error) {
        console.error('Error checking status:', error);
    }
}

function updateProcessingUI(status) {
    const progress = Math.round(status.progress * 100);
    
    document.getElementById('progressBar').value = progress;
    document.getElementById('progressCircle').style.setProperty('--value', progress);
    document.getElementById('progressCircle').textContent = progress + '%';
    document.getElementById('statusMessage').textContent = status.message;
    document.getElementById('progressDetails').textContent = 
        `Chunk ${status.current_chunk || 0} of ${status.total_chunks || 0} • ${status.cards_extracted || 0} cards extracted`;
}

function cancelProcessing() {
    if (processingInterval) {
        clearInterval(processingInterval);
        processingInterval = null;
    }
    
    document.getElementById('processingStatus').classList.add('hidden');
    document.getElementById('processBtn').disabled = false;
    currentSessionId = null;
}

function getSelectedElements() {
    const elements = [];
    document.querySelectorAll('[data-element]:checked').forEach(checkbox => {
        elements.push(checkbox.dataset.element);
    });
    return elements;
}

// Story Cards
async function loadStoryCards() {
    if (!currentFile) return;

    try {
        const response = await fetch(`/cards/${currentFile.filename}`);
        if (response.ok) {
            const data = await response.json();
            allCards = data.story_cards;
            populateFilters();
            filterCards();
            
            // Hide empty state
            document.getElementById('emptyState').classList.add('hidden');
        } else if (response.status === 404) {
            // No cards found
            allCards = [];
            document.getElementById('cardsContainer').innerHTML = '';
            document.getElementById('emptyState').classList.remove('hidden');
            updateCardCount(0);
        }
    } catch (error) {
        showAlert('Error loading story cards: ' + error.message, 'error');
    }
}

function populateFilters() {
    const categories = [...new Set(allCards.map(card => card.category))];
    const frameworks = [...new Set(allCards.map(card => card.framework))];

    const categorySelect = document.getElementById('categoryFilter');
    const frameworkSelect = document.getElementById('frameworkFilter');

    // Clear existing options (except "All")
    categorySelect.innerHTML = '<option value="">All Categories</option>';
    frameworkSelect.innerHTML = '<option value="">All Frameworks</option>';

    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        categorySelect.appendChild(option);
    });

    frameworks.forEach(framework => {
        const option = document.createElement('option');
        option.value = framework;
        option.textContent = framework;
        frameworkSelect.appendChild(option);
    });
}

function filterCards() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const categoryFilter = document.getElementById('categoryFilter').value;
    const frameworkFilter = document.getElementById('frameworkFilter').value;
    const minScore = parseInt(document.getElementById('scoreFilter').value);

    let filteredCards = allCards.filter(card => {
        const matchesSearch = !searchTerm || 
            card.title.toLowerCase().includes(searchTerm) ||
            card.content.toLowerCase().includes(searchTerm) ||
            card.key_insight.toLowerCase().includes(searchTerm);
        
        const matchesCategory = !categoryFilter || card.category === categoryFilter;
        const matchesFramework = !frameworkFilter || card.framework === frameworkFilter;
        const matchesScore = card.interest_score >= minScore;

        return matchesSearch && matchesCategory && matchesFramework && matchesScore;
    });

    // Sort by interest score
    filteredCards.sort((a, b) => b.interest_score - a.interest_score);

    displayCards(filteredCards);
    updateCardCount(filteredCards.length);
}

function displayCards(cards) {
    const container = document.getElementById('cardsContainer');
    container.innerHTML = '';

    cards.forEach((card, index) => {
        const cardElement = createCardElement(card, index);
        container.appendChild(cardElement);
    });

    // Animate cards
    anime({
        targets: '.story-card',
        opacity: [0, 1],
        translateY: [20, 0],
        duration: 500,
        delay: anime.stagger(50),
        easing: 'easeOutCubic'
    });
}

function createCardElement(card, index) {
    const div = document.createElement('div');
    div.className = 'story-card card bg-base-100 shadow-lg card-hover cursor-pointer';
    div.onclick = () => openCardModal(card);
    
    div.innerHTML = `
        <div class="card-body">
            <div class="flex justify-between items-start mb-2">
                <h2 class="card-title text-lg">${card.title}</h2>
                <div class="badge badge-primary">${card.interest_score}/10</div>
            </div>
            
            <div class="mb-3">
                <div class="badge badge-outline badge-sm mr-2">${card.framework}</div>
                <div class="badge badge-ghost badge-sm">${card.category}</div>
            </div>
            
            <p class="text-sm text-base-content/70 line-clamp-3 mb-3">${card.content}</p>
            
            <div class="mb-3">
                <div class="text-xs text-base-content/60 mb-1">Key Insight:</div>
                <div class="text-sm font-medium">${card.key_insight}</div>
            </div>
            
            <div class="card-actions justify-between items-center">
                <div class="text-xs text-base-content/50">
                    Pages ${card.page_reference.join(', ')}
                </div>
                <div class="flex gap-1">
                    ${card.sentiment ? `<div class="badge badge-xs badge-outline">${card.sentiment}</div>` : ''}
                    <div class="badge badge-xs">${card.emotional_core}</div>
                </div>
            </div>
        </div>
    `;
    
    return div;
}

function updateCardCount(count) {
    document.getElementById('cardCount').textContent = `${count} cards`;
}

// Modal
function openCardModal(card) {
    document.getElementById('modalTitle').textContent = card.title;
    showModalTab('overview', card);
    document.getElementById('cardModal').classList.add('modal-open');
}

function closeModal() {
    document.getElementById('cardModal').classList.remove('modal-open');
}

function showModalTab(tabName, card = null) {
    // Update tab buttons
    document.querySelectorAll('#cardModal .tab').forEach(tab => {
        tab.classList.remove('tab-active');
    });
    event.target.classList.add('tab-active');

    const modalContent = document.getElementById('modalContent');

    if (tabName === 'overview') {
        modalContent.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h4 class="font-semibold mb-2">Framework & Category</h4>
                    <div class="mb-4">
                        <div class="badge badge-primary mb-2">${card.framework}</div>
                        <div class="badge badge-outline">${card.category}</div>
                    </div>
                    
                    <h4 class="font-semibold mb-2">Emotional Core</h4>
                    <p class="mb-4">${card.emotional_core}</p>
                    
                    <h4 class="font-semibold mb-2">Content</h4>
                    <p class="mb-4">${card.content}</p>
                    
                    <h4 class="font-semibold mb-2">Key Insight</h4>
                    <p class="mb-4 font-medium text-primary">${card.key_insight}</p>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-2">Key Phrases</h4>
                    <div class="mb-4">
                        ${card.key_phrases.map(phrase => `<div class="quote mb-2">"${phrase}"</div>`).join('')}
                    </div>
                    
                    <h4 class="font-semibold mb-2">Audience Impact</h4>
                    <p class="mb-4">${card.audience_impact}</p>
                    
                    <h4 class="font-semibold mb-2">Contextual Relevance</h4>
                    <p class="mb-4">${card.contextual_relevance}</p>
                    
                    <h4 class="font-semibold mb-2">Details</h4>
                    <div class="text-sm">
                        <div><strong>Interest Score:</strong> ${card.interest_score}/10</div>
                        <div><strong>Pages:</strong> ${card.page_reference.join(', ')}</div>
                        <div><strong>Audience:</strong> ${card.audience.join(', ')}</div>
                        <div><strong>Sentiment:</strong> ${card.sentiment}</div>
                    </div>
                </div>
            </div>
        `;
    } else if (tabName === 'script') {
        modalContent.innerHTML = `
            <div class="space-y-4">
                <div>
                    <h4 class="font-semibold mb-2 text-primary">Hook</h4>
                    <div class="bg-base-200 p-4 rounded-lg">
                        ${card.script_hook || 'No hook available'}
                    </div>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-2 text-primary">Narrative</h4>
                    <div class="bg-base-200 p-4 rounded-lg">
                        ${card.script_narrative || 'No narrative available'}
                    </div>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-2 text-primary">Powerful Conclusion</h4>
                    <div class="bg-base-200 p-4 rounded-lg">
                        ${card.script_conclusion || 'No conclusion available'}
                    </div>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-2 text-primary">Delivery Notes</h4>
                    <div class="bg-base-200 p-4 rounded-lg">
                        ${card.script_delivery || 'No delivery notes available'}
                    </div>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-2 text-primary">Complete Script</h4>
                    <textarea class="textarea textarea-bordered w-full h-32" readonly>${card.storyteller_script}</textarea>
                </div>
            </div>
        `;
    } else if (tabName === 'details') {
        modalContent.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h4 class="font-semibold mb-2">Keywords</h4>
                    <div class="mb-4">
                        ${card.keywords.map(keyword => `<div class="badge badge-outline mr-1 mb-1">${keyword}</div>`).join('')}
                    </div>
                    
                    <h4 class="font-semibold mb-2">Entities</h4>
                    <div class="mb-4">
                        ${card.entities.map(entity => `<div class="badge badge-ghost mr-1 mb-1">${entity}</div>`).join('')}
                    </div>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-2">Source Information</h4>
                    <div class="bg-base-200 p-4 rounded-lg">
                        <div><strong>Source:</strong> ${card.source}</div>
                        <div><strong>Page Reference:</strong> ${card.page_reference.join(', ')}</div>
                        <div><strong>Sentiment:</strong> ${card.sentiment}</div>
                        <div><strong>Suitable Audience:</strong> ${card.audience.join(', ')}</div>
                    </div>
                </div>
            </div>
        `;
    }
}

// Analytics
function loadAnalytics() {
    if (!allCards.length) {
        return;
    }

    // Update stats
    document.getElementById('totalCards').textContent = allCards.length;
    
    const avgScore = allCards.reduce((sum, card) => sum + card.interest_score, 0) / allCards.length;
    document.getElementById('avgScore').textContent = avgScore.toFixed(1);
    
    const categories = new Set(allCards.map(card => card.category));
    document.getElementById('categoriesCount').textContent = categories.size;
    
    const highInterest = allCards.filter(card => card.interest_score >= 8).length;
    document.getElementById('highInterest').textContent = highInterest;

    // Simple chart representations
    renderCategoryChart();
    renderScoreChart();
}

function renderCategoryChart() {
    const categoryCount = {};
    allCards.forEach(card => {
        categoryCount[card.category] = (categoryCount[card.category] || 0) + 1;
    });

    const chartContainer = document.getElementById('categoryChart');
    chartContainer.innerHTML = '';

    Object.entries(categoryCount).forEach(([category, count]) => {
        const percentage = (count / allCards.length) * 100;
        const bar = document.createElement('div');
        bar.className = 'flex items-center mb-2';
        bar.innerHTML = `
            <div class="w-24 text-xs truncate">${category}</div>
            <div class="flex-1 bg-base-300 rounded-full h-4 mx-2">
                <div class="bg-primary h-4 rounded-full" style="width: ${percentage}%"></div>
            </div>
            <div class="w-8 text-xs">${count}</div>
        `;
        chartContainer.appendChild(bar);
    });
}

function renderScoreChart() {
    const scoreCount = {};
    for (let i = 1; i <= 10; i++) {
        scoreCount[i] = 0;
    }
    
    allCards.forEach(card => {
        scoreCount[card.interest_score] = (scoreCount[card.interest_score] || 0) + 1;
    });

    const chartContainer = document.getElementById('scoreChart');
    chartContainer.innerHTML = '';

    Object.entries(scoreCount).forEach(([score, count]) => {
        const percentage = allCards.length > 0 ? (count / allCards.length) * 100 : 0;
        const bar = document.createElement('div');
        bar.className = 'flex items-center mb-1';
        bar.innerHTML = `
            <div class="w-8 text-xs">${score}</div>
            <div class="flex-1 bg-base-300 rounded-full h-3 mx-2">
                <div class="bg-secondary h-3 rounded-full" style="width: ${percentage}%"></div>
            </div>
            <div class="w-8 text-xs">${count}</div>
        `;
        chartContainer.appendChild(bar);
    });
}

// Export functions
async function exportMarkdown() {
    if (!currentFile) return;

    try {
        const response = await fetch(`/export/${currentFile.filename}/markdown`);
        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${currentFile.filename.replace('.pdf', '')}_storyteller_cards.md`;
            a.click();
            window.URL.revokeObjectURL(url);
            showAlert('Markdown exported successfully', 'success');
        } else {
            showAlert('Error exporting markdown', 'error');
        }
    } catch (error) {
        showAlert('Error exporting markdown: ' + error.message, 'error');
    }
}

function exportJSON() {
    if (!allCards.length) return;

    const dataStr = JSON.stringify({story_cards: allCards}, null, 2);
    const blob = new Blob([dataStr], {type: "application/json"});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${currentFile.filename.replace('.pdf', '')}_cards.json`;
    a.click();
    URL.revokeObjectURL(url);
    showAlert('JSON exported successfully', 'success');
}

// Utility functions
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} fixed top-4 right-4 w-auto max-w-md z-50`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Animate in
    anime({
        targets: alertDiv,
        opacity: [0, 1],
        translateX: [300, 0],
        duration: 300,
        easing: 'easeOutCubic'
    });
    
    // Remove after 3 seconds
    setTimeout(() => {
        anime({
            targets: alertDiv,
            opacity: [1, 0],
            translateX: [0, 300],
            duration: 300,
            easing: 'easeInCubic',
            complete: () => alertDiv.remove()
        });
    }, 3000);
}
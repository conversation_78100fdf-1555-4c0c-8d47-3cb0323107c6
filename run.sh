#!/bin/bash

echo "🃏 Storyteller Tactics Card Generator"
echo "=================================================="

# Check if API key is set
if [ -z "$GEMINI_API_KEY" ]; then
    echo "❌ GEMINI_API_KEY environment variable not set"
    echo "Please set your OpenAI API key first:"
    echo "export GEMINI_API_KEY=your_api_key_here"
    echo ""
    echo "Then run this script again."
    exit 1
fi

echo "✅ API key found"

# Install dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed"

# Start server
echo "🚀 Starting server..."
echo "Server will be available at: http://localhost:8000"
echo "Press Ctrl+C to stop the server"
echo ""

# Try to open browser (works on macOS and some Linux distros)
if command -v open &> /dev/null; then
    # macOS
    sleep 3 && open http://localhost:8000 &
elif command -v xdg-open &> /dev/null; then
    # Linux
    sleep 3 && xdg-open http://localhost:8000 &
fi

# Start server
uvicorn main:app --reload --host 0.0.0.0 --port 8000